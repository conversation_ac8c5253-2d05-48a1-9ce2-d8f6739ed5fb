#SingleInstance Force

server_user=admin
server_pass=Z4Q02Oeb70ib
server_ip=***********
server_port=8080
connect_timeout=5
retry_count = 3

IniRead, saved_mnt, vfs.ini, Config, MountPath, c:\apps
IniRead, server_user, vfs.ini, Config, server_user, %server_user%
IniRead, server_pass, vfs.ini, Config, server_pass, %server_pass%
IniRead, server_ip, vfs.ini, Config, server_ip, %server_ip%
IniRead, server_port, vfs.ini, Config, server_port, %server_port%
IniRead, connect_timeout, vfs.ini, Config, connect_timeout, %connect_timeout%
IniRead, retry_count, vfs.ini, Config, retry_count, %retry_count%

RunWait, %comspec% /c bin\curl.exe --retry %retry_count% --connect-timeout %connect_timeout% --user "%server_user%:%server_pass%" "%server_ip%:%server_port%/config?mac=%MAC%" | clip,,hide
RetVal = %clipboard%
if RetVal is not space
{
	Array := StrSplit(RetVal, ",")
	IP := Array[1]
	SUBNET_MASK := Array[2]
	GATEWAY := Array[3]
	DNS := Array[4]
	NAME := Array[5]
	SHELL := Array[6]
	CUSTOM_SHELL := Array[7]
	
	if (SHELL != "")
	{
		if FileExist("vfs.exe") 
		{
			RunWait, vfs.exe Z4Q02Oeb70ib, , Hide UseErrorLevel
			RetError = %ErrorLevel%
			If RetError
			{
				FileAppend, %A_Now%: VFS Error %RetError%`n, ConfigClient.log
			}	
		}	
		Else
		{
			FileAppend, %A_Now%: VFS vfs.exe not found`n, ConfigClient.log
		}
	}
	Else
	{
		if FileExist("vfs.exe") 
			RunWait, vfs.exe, , Hide UseErrorLevel
		
		FileAppend, %A_Now%: No shell!`n, ConfigClient.log
	}
}
Else
{
	if FileExist("vfs.exe") 
		RunWait, vfs.exe, , Hide UseErrorLevel
	
	SplashTextOn,,, Unable to get any data from server!
	FileAppend, %A_Now%: Unable to get any data from server!`n, ConfigClient.log
	SHELL=%fallback_shell%
}	